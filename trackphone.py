try:
    import phonenumbers
    from phonenumbers import geocoder
except ImportError:
    print("Error: phonenumbers package is not installed.")
    print("Please install it using: pip install phonenumbers")
    exit(1)

def track_phone_location(phone_number_str):
    """
    Track the location of a phone number.

    Args:
        phone_number_str (str): Phone number in international format (e.g., "+250788948884")

    Returns:
        str: Location description of the phone number
    """
    try:
        # Parse the phone number
        phone_number = phonenumbers.parse(phone_number_str)

        # Check if the number is valid
        if not phonenumbers.is_valid_number(phone_number):
            return f"Invalid phone number: {phone_number_str}"

        # Get location description
        location = geocoder.description_for_number(phone_number, "en")

        if location:
            return f"Phone number {phone_number_str} is from: {location}"
        else:
            return f"Location not found for phone number: {phone_number_str}"

    except phonenumbers.NumberParseException as e:
        return f"Error parsing phone number {phone_number_str}: {e}"
    except Exception as e:
        return f"Unexpected error: {e}"

if __name__ == "__main__":
    # Example phone number
    phone_number1 = "+250788948884"

    print("\n=== Phone Number Location Tracker ===\n")

    # Track the location
    result = track_phone_location(phone_number1)
    print(result)

    # You can add more phone numbers here
    # phone_number2 = "+1234567890"
    # result2 = track_phone_location(phone_number2)
    # print(result2)

