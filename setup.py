#!/usr/bin/env python3
"""
Setup script for phone location tracker.
This script will install the required dependencies.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages from requirements.txt"""
    try:
        print("Installing required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False
    except FileNotFoundError:
        print("❌ pip not found. Please install Python and pip first.")
        return False

def main():
    print("=== Phone Location Tracker Setup ===\n")
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found!")
        return
    
    # Install requirements
    if install_requirements():
        print("\n🎉 Setup complete! You can now run:")
        print("   python trackphone.py")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
