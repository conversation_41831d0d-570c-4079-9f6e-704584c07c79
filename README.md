# Phone Location Tracker

A Python application to track the geographical location of phone numbers using the `phonenumbers` library.

## Features

- Track location of phone numbers by country/region
- Validate phone number format
- Handle errors gracefully
- Support for international phone number formats

## Setup

### Option 1: Automatic Setup
Run the setup script to install dependencies automatically:
```bash
python setup.py
```

### Option 2: Manual Setup
1. Install the required package:
```bash
pip install phonenumbers
```

Or install from requirements.txt:
```bash
pip install -r requirements.txt
```

## Usage

Run the phone tracker:
```bash
python trackphone.py
```

The script will track the location of the example phone number `+250788948884`.

### Adding More Phone Numbers

You can modify the `trackphone.py` file to track additional phone numbers:

```python
# Add more phone numbers
phone_numbers = [
    "+250788948884",  # Rwanda
    "+1234567890",    # US/Canada
    "+447911123456"   # UK
]

for number in phone_numbers:
    result = track_phone_location(number)
    print(result)
```

## Phone Number Format

Phone numbers should be in international format starting with `+` followed by the country code:
- `+250788948884` (Rwanda)
- `+1234567890` (US/Canada)
- `+447911123456` (UK)
- `+33123456789` (France)

## Error Handling

The application handles various error cases:
- Missing `phonenumbers` package
- Invalid phone number format
- Phone numbers that cannot be parsed
- Unknown locations

## Requirements

- Python 3.6+
- phonenumbers library

## Files

- `trackphone.py` - Main application
- `requirements.txt` - Python dependencies
- `setup.py` - Setup script
- `README.md` - This file
